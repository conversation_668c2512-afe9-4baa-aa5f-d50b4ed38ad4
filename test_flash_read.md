# Flash读取功能测试指南

## 测试步骤

### 1. 启动应用
```bash
bun run tauri dev
```

### 2. 连接串口
1. 在应用界面中选择正确的串口
2. 设置波特率为 115200
3. 点击"连接"按钮

### 3. 测试一步到位读取
1. 确保串口连接成功
2. 点击"一步到位Flash读取"按钮
3. 观察进度和结果

### 4. 验证结果
- 检查下载目录中的 `flash_onestep_*.bin` 文件
- 查看控制台日志中的速度和耗时信息
- 验证文件大小是否符合预期

## 测试参数

当前默认测试参数：
- 起始地址：`0x10000` (64KB)
- 总长度：`0x7F0000` (约8MB-64KB)
- 块大小：`256` 字节

## 预期结果

### 成功情况
- 文件大小：约8MB
- 速度：取决于串口和设备性能
- 无错误信息

### 可能的警告
- CRC校验失败警告（不影响功能）
- 个别块读取重试

## 对比测试

可以同时测试两种模式：
1. "下载Flash数据 (优化模式)" - 传统分步方式
2. "一步到位Flash读取" - 新的统一方式

比较两者的：
- 执行时间
- 文件大小
- 错误率
- 用户体验

## 故障排除

如果测试失败：
1. 检查串口连接
2. 确认设备响应
3. 查看控制台错误信息
4. 尝试调整块大小（128或64字节）
