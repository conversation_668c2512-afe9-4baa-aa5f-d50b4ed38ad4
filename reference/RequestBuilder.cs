﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace WPPGBase.Models
{
    public class RequestBuilder : SerialMessage 
    {
        public WPPGCommandType cmdType;

        //Total bytes count of response array
        public int ExpectedLen 
        {
            get { return 5 + contentLen; }
        }

        public override string MessageTypeString => cmdType.ToString();
        public override bool IsRequest => true;
        public override byte[] Bytes => GetSendData();

        private byte[] data;
        private byte[] payload;

        //Total bytes count of request array 
        private int PayLen=5;
        //Payload bytes count of response data
        private int contentLen=0;

        private bool varLenTx = false;
        private bool varLenRx = false;

        private void setLen(WPPGCommandType cmd)
        {
            switch (cmd)
            {
                case WPPGCommandType.READ_PRESSURE_RAW:
                case WPPGCommandType.FW_REVISION:

                case WPPGCommandType.READ_BATT:
                case WPPGCommandType.HD_REVISION:
                    contentLen = 4;
                    break;
                case WPPGCommandType.READ_PRESSURE:
                case WPPGCommandType.READ_TEMP:

                    contentLen = 2;
                    break;
                case WPPGCommandType.FLASH_ERASE:
                    PayLen = 7;

                    break;
                case WPPGCommandType.FLASH_READ:
                    PayLen = 11;
                    varLenRx = true;
                    break;
                case WPPGCommandType.FLASH_WRITE:                    
                    varLenTx = true;

                    break;
                case WPPGCommandType.FLASH_STATUS:

                    contentLen = 8;
                    break;
                case WPPGCommandType.LOG_ENABLE:
                case WPPGCommandType.LOG_DISABLE:
                case WPPGCommandType.RESET:
                case WPPGCommandType.SET_DATE_TIME:
                    PayLen = 9;

                    break;
         
                case WPPGCommandType.CONFIG_READ:
                    PayLen = 7;
                    contentLen = 8;
                    break;
                case WPPGCommandType.CONFIG_WRITE:
                    PayLen = 13;

                    break;
                default:
                    break;
            }

        }
        
        public void SetPayload(byte[] payload)
        {
            this.payload = payload;
            if (varLenRx)
            {
                contentLen = payload[4] + (payload[5] << 8);
            }
            if (varLenTx)
            {
                PayLen = 11 + payload[4] + (payload[5] << 8);
            }
        }

        public RequestBuilder(WPPGCommandType cmd)
        {
            cmdType = cmd;
            setLen(cmdType);
            payload = new byte[0];
        }

        public RequestBuilder(WPPGCommandType cmd, byte[] payload)
        {
            cmdType = cmd;
            setLen(cmdType);
            SetPayload(payload);
        }

        private byte[] GetSendData()
        {
            int Len = payload.Length;
            
            //数据和命令不匹配
            if (PayLen - 5 != Len)
            {
                return null;
            }

            data = new byte[PayLen];
            data[0] =(byte)WPPGHeaderID.WPPGXmitter;
            data[1] = (byte)cmdType;
            data[2] = (byte)(Len & 0xFF);
            data[3] = (byte)((Len & 0xFF00) >> 8);
            for (int i = 0; i < Len; i++)
            {
                data[i + 4] = payload[i];
            }
            data[Len + 4] = Util.CRC_calc(data);

            return data;
        }


        public ResponseDecoder GetResponse()
        {
            return new ResponseDecoder(cmdType);
        }

        public static byte[] GetUtcTimeData(DateTime dateTime)
        {
            uint UtcTime = (uint)(dateTime.ToUniversalTime() - new DateTime(1970, 1, 1, 0, 0, 0, DateTimeKind.Utc)).TotalSeconds;
            return BitConverter.GetBytes(UtcTime);
        }

        public static byte[] GetFlashReadData(uint addr ,int len)
        {
            byte[] data = new byte[6];
            Buffer.BlockCopy(BitConverter.GetBytes(addr), 0, data, 0, 4);          
            data[4] = (byte)(len & 0xFF);
            data[5] = (byte)((len & 0xFF00) >> 8);
            return data;
        }
        public static byte[] GetFlashWriteData(uint addr, int len, byte[] pay)
        {
            if (len != pay.Length)
            {
                return null;
            }
            byte[] data = new byte[6+len];
            Buffer.BlockCopy(BitConverter.GetBytes(addr), 0, data, 0, 4);
            data[4] = (byte)(len & 0xFF);
            data[5] = (byte)((len & 0xFF00) >> 8);
            Buffer.BlockCopy(pay, 0, data, 6, len);

            return data;
        }
        public static byte[] GetFlashEraseData(int sector)
        {
            byte RFU = 0;
            byte[] data = new byte[2];
            data[0] = (byte)sector;
            data[1] = RFU;
            return data;
        }
        public static byte[] GetConfigReadData(WPPGConfigurationType cfg)
        {
            byte[] data = new byte[2];
            data[0] = (byte)cfg;
            data[1] = (byte)(((ushort)cfg & 0xFF00) >> 8);
            return data;
        }
        public static byte[] GetConfigWriteData(WPPGConfigurationType cfg, byte[] content)
        {
            if (6 != content.Length)
            {
                return null;
            }
            byte[] data = new byte[8];
            data[0] = (byte)cfg;
            data[1] = (byte)(((ushort)cfg & 0xFF00) >> 8);
            Buffer.BlockCopy(content, 0, data, 2, 6);

            return data;
        }

    }
}
