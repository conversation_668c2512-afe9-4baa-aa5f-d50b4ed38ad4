StartAddr = 0x10000;
BytesBlock2 = new byte[MaxAddr - 0x10000];
addr = StartAddr;
do
{  
    if ((addr + LongRead) <= MaxAddr)
    {
        byte[] xRead = await m_api.WPPGFlashRead(addr, API.LEN_LOGGING_READ);
    
        xRead.CopyTo(BytesBlock2, addr - StartAddr);
    }
    else
    {
        byte[] xRead = await m_api.WPPGFlashRead(addr, (int)(MaxAddr - addr));
       
        xRead.CopyTo(BytesBlock2, addr - StartAddr);
    }
    addr += LongRead;

} while (addr < MaxAddr);

ttn.SaveLogs(BytesBlock2);


 public async Task<byte[]> WPPGFlashRead(uint StartAddr, int len)
 {
   
     byte[] payload = RequestBuilder.GetFlashReadData(StartAddr, len);
     RequestBuilder request = new RequestBuilder(WPPGCommandType.FLASH_READ, payload);
     var r = await WPPGSerialPort.Instance.SendCmdAsyn(request);
   

     byte[] bytes = r.PayLoadBytes;

     return bytes;
 }

 public static byte[] GetFlashReadData(uint addr ,int len)
 {
     byte[] data = new byte[6];
     Buffer.BlockCopy(BitConverter.GetBytes(addr), 0, data, 0, 4);          
     data[4] = (byte)(len & 0xFF);
     data[5] = (byte)((len & 0xFF00) >> 8);
     return data;
 }
