use log::{info, warn, error, debug};
use std::io::Write;


// Flash协议相关的类型定义
#[derive(Debug, <PERSON><PERSON>, Copy, PartialEq, Eq)]
pub enum FlashCommandType {
    Read = 0x08,  // Flash读取使用0x08 (256字节)
    Write = 0x55,
    Erase = 0x56,
    Status = 0x57,
}

#[derive(Debu<PERSON>, <PERSON><PERSON>)]
pub struct FlashResponse {
    pub command_type: FlashCommandType,
    pub status: u8,
    pub data: Vec<u8>,
    pub crc_valid: bool,
}

// 协议常量
const PROTOCOL_HEADER: u8 = 0x77;

// CRC8计算表 (基于C#代码)
const CRC8_TABLE: [u8; 256] = [
    0x00, 0x07, 0x0E, 0x09, 0x1C, 0x1B, 0x12, 0x15, 0x38, 0x3F, 0x36, 0x31, 0x24, 0x23, 0x2A, 0x2D,
    0x70, 0x77, 0x7E, 0x79, 0x6C, 0x6B, 0x62, 0x65, 0x48, 0x4F, 0x46, 0x41, 0x54, 0x53, 0x5A, 0x5D,
    0xE0, 0xE7, 0xEE, 0xE9, 0xFC, 0xFB, 0xF2, 0xF5, 0xD8, 0xDF, 0xD6, 0xD1, 0xC4, 0xC3, 0xCA, 0xCD,
    0x90, 0x97, 0x9E, 0x99, 0x8C, 0x8B, 0x82, 0x85, 0xA8, 0xAF, 0xA6, 0xA1, 0xB4, 0xB3, 0xBA, 0xBD,
    0xC7, 0xC0, 0xC9, 0xCE, 0xDB, 0xDC, 0xD5, 0xD2, 0xFF, 0xF8, 0xF1, 0xF6, 0xE3, 0xE4, 0xED, 0xEA,
    0xB7, 0xB0, 0xB9, 0xBE, 0xAB, 0xAC, 0xA5, 0xA2, 0x8F, 0x88, 0x81, 0x86, 0x93, 0x94, 0x9D, 0x9A,
    0x27, 0x20, 0x29, 0x2E, 0x3B, 0x3C, 0x35, 0x32, 0x1F, 0x18, 0x11, 0x16, 0x03, 0x04, 0x0D, 0x0A,
    0x57, 0x50, 0x59, 0x5E, 0x4B, 0x4C, 0x45, 0x42, 0x6F, 0x68, 0x61, 0x66, 0x73, 0x74, 0x7D, 0x7A,
    0x89, 0x8E, 0x87, 0x80, 0x95, 0x92, 0x9B, 0x9C, 0xB1, 0xB6, 0xBF, 0xB8, 0xAD, 0xAA, 0xA3, 0xA4,
    0xF9, 0xFE, 0xF7, 0xF0, 0xE5, 0xE2, 0xEB, 0xEC, 0xC1, 0xC6, 0xCF, 0xC8, 0xDD, 0xDA, 0xD3, 0xD4,
    0x69, 0x6E, 0x67, 0x60, 0x75, 0x72, 0x7B, 0x7C, 0x51, 0x56, 0x5F, 0x58, 0x4D, 0x4A, 0x43, 0x44,
    0x19, 0x1E, 0x17, 0x10, 0x05, 0x02, 0x0B, 0x0C, 0x21, 0x26, 0x2F, 0x28, 0x3D, 0x3A, 0x33, 0x34,
    0x4E, 0x49, 0x40, 0x47, 0x52, 0x55, 0x5C, 0x5B, 0x76, 0x71, 0x78, 0x7F, 0x6A, 0x6D, 0x64, 0x63,
    0x3E, 0x39, 0x30, 0x37, 0x22, 0x25, 0x2C, 0x2B, 0x06, 0x01, 0x08, 0x0F, 0x1A, 0x1D, 0x14, 0x13,
    0xAE, 0xA9, 0xA0, 0xA7, 0xB2, 0xB5, 0xBC, 0xBB, 0x96, 0x91, 0x98, 0x9F, 0x8A, 0x8D, 0x84, 0x83,
    0xDE, 0xD9, 0xD0, 0xD7, 0xC2, 0xC5, 0xCC, 0xCB, 0xE6, 0xE1, 0xE8, 0xEF, 0xFA, 0xFD, 0xF4, 0xF3
];

// CRC8计算函数 (基于C#代码实现)
fn calculate_crc8(data: &[u8]) -> u8 {
    let mut crc = 0u8;
    // 注意：CRC计算不包含最后一个字节（CRC字节本身）
    let length = data.len() - 1;
    for i in 0..length {
        crc = CRC8_TABLE[(crc ^ data[i]) as usize];
    }
    crc
}



#[tauri::command]
async fn debug_serial_connection(port_path: String, baud_rate: u32) -> Result<String, String> {
    info!("🔍 开始调试串口连接: {}", port_path);
    info!("📊 连接参数: 波特率={}, 数据位=8, 停止位=1, 校验位=None", baud_rate);

    // 检查串口是否存在
    match std::fs::metadata(&port_path) {
        Ok(metadata) => {
            info!("✅ 串口设备文件存在: {}", port_path);
            debug!("📁 设备文件信息: {:?}", metadata);
        }
        Err(e) => {
            error!("❌ 串口设备文件不存在: {} - 错误: {}", port_path, e);
            return Err(format!("设备文件不存在: {}", e));
        }
    }

    // 尝试获取系统串口列表
    info!("🔍 获取系统可用串口列表...");

    // 尝试使用系统命令检查串口状态
    info!("🔍 检查串口是否被其他进程占用...");
    match std::process::Command::new("lsof")
        .arg(&port_path)
        .output() {
        Ok(output) => {
            let stdout = String::from_utf8_lossy(&output.stdout);
            let stderr = String::from_utf8_lossy(&output.stderr);
            if !stdout.is_empty() {
                warn!("⚠️ 串口可能被其他进程占用: {}", stdout.trim());
            } else {
                info!("✅ 串口未被其他进程占用");
            }
            if !stderr.is_empty() {
                debug!("lsof stderr: {}", stderr.trim());
            }
        }
        Err(e) => {
            warn!("⚠️ 无法检查串口占用状态: {}", e);
        }
    }

    // 尝试直接使用serialport库测试连接
    info!("🔍 尝试使用底层serialport库测试连接...");
    match serialport::new(&port_path, baud_rate)
        .timeout(std::time::Duration::from_millis(1000))
        .open() {
        Ok(mut port) => {
            info!("✅ 底层serialport库连接成功！");
            // 尝试写入测试数据
            match port.write(b"test") {
                Ok(bytes_written) => {
                    info!("✅ 测试写入成功，写入 {} 字节", bytes_written);
                }
                Err(e) => {
                    warn!("⚠️ 测试写入失败: {}", e);
                }
            }
        }
        Err(e) => {
            error!("❌ 底层serialport库连接失败: {}", e);
            return Err(format!("底层连接失败: {}", e));
        }
    }

    Ok(format!("串口调试完成，详细信息请查看终端日志"))
}

// Flash协议处理模块 (保留结构定义，但使用简化的命令构建)

impl FlashResponse {
    /// 从字节数组解析响应
    pub fn from_bytes(data: &[u8]) -> Result<Self, String> {
        if data.len() < 5 {
            return Err("响应数据太短".to_string());
        }

        // 检查header
        if data[0] != PROTOCOL_HEADER {
            return Err(format!("无效的协议头: 期望0x{:02X}, 实际0x{:02X}", PROTOCOL_HEADER, data[0]));
        }

        let command_type = match data[1] {
            0x08 => FlashCommandType::Read,  // Flash读取使用0x08
            0x55 => FlashCommandType::Write,
            0x56 => FlashCommandType::Erase,
            0x57 => FlashCommandType::Status,
            other => return Err(format!("未知的命令类型: 0x{:02X}", other)),
        };

        // 获取payload长度
        let payload_length = data[2] as usize | ((data[3] as usize) << 8);

        if data.len() < 4 + payload_length + 1 {
            return Err("响应数据不完整".to_string());
        }

        // 验证CRC
        let expected_crc = calculate_crc8(&data[..data.len()-1]);
        let actual_crc = data[data.len()-1];
        let crc_valid = expected_crc == actual_crc;

        if !crc_valid {
            warn!("⚠️ CRC校验失败: 期望={:02X}, 实际={:02X}", expected_crc, actual_crc);
        }

        // 提取状态和数据
        let status = if payload_length > 0 { data[4] } else { 0 };
        let response_data = if payload_length > 1 {
            data[5..4+payload_length].to_vec()
        } else {
            Vec::new()
        };

        debug!("解析Flash响应: 类型={:?}, 状态=0x{:02X}, 数据长度={}, CRC有效={}",
              command_type, status, response_data.len(), crc_valid);

        Ok(FlashResponse {
            command_type,
            status,
            data: response_data,
            crc_valid,
        })
    }
}

// 构建Flash读取命令 (基于C#的GetFlashReadData和GetSendData，包含发送前校验)
#[tauri::command]
fn build_flash_read_command(addr: u32, len: u16) -> Result<Vec<u8>, String> {
    // 根据C#代码中的setLen方法，FLASH_READ命令的PayLen = 11
    const FLASH_READ_PAY_LEN: usize = 11;
    const FLASH_READ_PAYLOAD_LEN: usize = 6; // PayLen - 5 = 11 - 5 = 6

    // 发送前校验：验证payload长度是否与命令类型匹配
    // 对应C#代码: if (PayLen - 5 != Len) { return null; }
    if FLASH_READ_PAYLOAD_LEN != 6 {
        error!("❌ Flash读取命令校验失败: payload长度不匹配，期望6字节，实际{}字节", FLASH_READ_PAYLOAD_LEN);
        return Err("Flash读取命令数据长度校验失败".to_string());
    }

    let mut command = vec![0u8; FLASH_READ_PAY_LEN];

    // Header
    command[0] = 0x77; // WPPGXmitter (对应C#的WPPGHeaderID.WPPGXmitter)
    command[1] = 0x08; // Flash读取使用0x08命令类型

    // Payload长度 (对应C#代码中的Len)
    command[2] = (FLASH_READ_PAYLOAD_LEN & 0xFF) as u8;
    command[3] = ((FLASH_READ_PAYLOAD_LEN & 0xFF00) >> 8) as u8;

    // Payload: 地址 (4字节，小端序) - 对应C#的GetFlashReadData
    command[4] = (addr & 0xFF) as u8;
    command[5] = ((addr >> 8) & 0xFF) as u8;
    command[6] = ((addr >> 16) & 0xFF) as u8;
    command[7] = ((addr >> 24) & 0xFF) as u8;

    // Payload: 长度 (2字节，小端序) - 对应C#的GetFlashReadData
    command[8] = (len & 0xFF) as u8;
    command[9] = ((len >> 8) & 0xFF) as u8;

    // CRC计算 (对应C#代码: data[Len + 4] = Util.CRC_calc(data))
    command[10] = calculate_crc8(&command);

    debug!("Flash读取命令构建: 地址=0x{:X}, 长度={}", addr, len);

    Ok(command)
}

// 解析Flash响应数据 (使用新的结构化方法)
#[tauri::command]
fn parse_flash_response(response_data: Vec<u8>) -> Result<Vec<u8>, String> {
    match FlashResponse::from_bytes(&response_data) {
        Ok(response) => {
            if !response.crc_valid {
                warn!("⚠️ 收到CRC校验失败的响应，但仍然返回数据");
            }
            Ok(response.data)
        }
        Err(e) => Err(e)
    }
}

// 验证CRC
#[tauri::command]
fn verify_crc(data: Vec<u8>) -> bool {
    if data.is_empty() {
        return false;
    }

    let expected_crc = calculate_crc8(&data[..data.len()-1]);
    let actual_crc = data[data.len()-1];

    expected_crc == actual_crc
}

// 验证Flash数据完整性
#[tauri::command]
fn validate_flash_data(data: Vec<u8>) -> Result<String, String> {
    if data.is_empty() {
        return Err("数据为空".to_string());
    }

    let mut report = Vec::new();

    // 基本统计
    report.push(format!("📊 数据大小: {} 字节", data.len()));

    // 检查数据模式
    let zero_count = data.iter().filter(|&&b| b == 0x00).count();
    let ff_count = data.iter().filter(|&&b| b == 0xFF).count();
    let zero_percentage = (zero_count as f32 / data.len() as f32) * 100.0;
    let ff_percentage = (ff_count as f32 / data.len() as f32) * 100.0;

    report.push(format!("🔍 数据分析:"));
    report.push(format!("  - 0x00 字节: {} ({:.1}%)", zero_count, zero_percentage));
    report.push(format!("  - 0xFF 字节: {} ({:.1}%)", ff_count, ff_percentage));

    // 检查是否全为空数据
    if zero_percentage > 95.0 {
        report.push("⚠️  警告: 数据几乎全为0x00，可能是空Flash区域".to_string());
    } else if ff_percentage > 95.0 {
        report.push("⚠️  警告: 数据几乎全为0xFF，可能是未编程的Flash区域".to_string());
    } else {
        report.push("✅ 数据看起来包含有效内容".to_string());
    }

    // 寻找可能的数据结构
    let mut patterns = Vec::new();

    // 检查常见的文件头
    if data.len() >= 4 {
        let header = &data[0..4];
        match header {
            [0x7F, 0x45, 0x4C, 0x46] => patterns.push("ELF可执行文件".to_string()),
            [0x50, 0x4B, 0x03, 0x04] => patterns.push("ZIP压缩文件".to_string()),
            [0x89, 0x50, 0x4E, 0x47] => patterns.push("PNG图像文件".to_string()),
            [0xFF, 0xD8, 0xFF, _] => patterns.push("JPEG图像文件".to_string()),
            _ => {}
        }
    }

    // 检查重复模式
    let mut pattern_found = false;
    for window_size in [4, 8, 16, 32] {
        if data.len() >= window_size * 2 {
            for i in 0..=(data.len() - window_size * 2) {
                let pattern1 = &data[i..i + window_size];
                let pattern2 = &data[i + window_size..i + window_size * 2];
                if pattern1 == pattern2 {
                    patterns.push(format!("重复模式 ({}字节): {:02X?}", window_size, &pattern1[..std::cmp::min(8, window_size)]));
                    pattern_found = true;
                    break;
                }
            }
            if pattern_found { break; }
        }
    }

    if !patterns.is_empty() {
        report.push("🔍 检测到的数据模式:".to_string());
        for pattern in patterns {
            report.push(format!("  - {}", pattern));
        }
    }

    // 计算简单校验和
    let checksum: u32 = data.iter().map(|&b| b as u32).sum();
    report.push(format!("🔢 数据校验和: 0x{:08X}", checksum));

    // 显示前16字节的十六进制转储
    if data.len() >= 16 {
        report.push("📋 前16字节十六进制转储:".to_string());
        let hex_dump = data[0..16].iter()
            .map(|b| format!("{:02X}", b))
            .collect::<Vec<_>>()
            .join(" ");
        report.push(format!("  {}", hex_dump));
    }

    Ok(report.join("\n"))
}

// 生成数据摘要
#[tauri::command]
fn generate_data_summary(data: Vec<u8>, start_addr: u32) -> String {
    if data.is_empty() {
        return "数据为空".to_string();
    }

    let end_addr = start_addr + data.len() as u32 - 1;
    let mut summary = Vec::new();

    summary.push(format!("📍 Flash地址范围: 0x{:08X} - 0x{:08X}", start_addr, end_addr));
    summary.push(format!("📏 数据大小: {} 字节 ({:.1} KB)", data.len(), data.len() as f32 / 1024.0));

    // 分析数据分布
    let mut byte_counts = [0u32; 256];
    for &byte in &data {
        byte_counts[byte as usize] += 1;
    }

    // 找出最常见的字节值
    let mut most_common = (0u8, 0u32);
    for (byte, &count) in byte_counts.iter().enumerate() {
        if count > most_common.1 {
            most_common = (byte as u8, count);
        }
    }

    let percentage = (most_common.1 as f32 / data.len() as f32) * 100.0;
    summary.push(format!("🔍 最常见字节: 0x{:02X} (出现{}次, {:.1}%)", most_common.0, most_common.1, percentage));

    // 计算熵（数据随机性）
    let mut entropy = 0.0;
    for &count in &byte_counts {
        if count > 0 {
            let p = count as f64 / data.len() as f64;
            entropy -= p * p.log2();
        }
    }
    summary.push(format!("📊 数据熵: {:.2} bits (0=完全有序, 8=完全随机)", entropy));

    summary.join("\n")
}

// 保存数据到文件
#[tauri::command]
async fn save_data_to_file(data: Vec<u8>, filename: String) -> Result<String, String> {
    info!("💾 保存数据到文件: {}", filename);

    use std::fs::File;
    use std::io::Write;

    // 获取用户下载目录
    let downloads_dir = dirs::download_dir()
        .ok_or_else(|| "无法获取下载目录".to_string())?;

    let file_path = downloads_dir.join(&filename);

    let mut file = File::create(&file_path).map_err(|e| {
        error!("❌ 创建文件失败: {}", e);
        format!("创建文件失败: {}", e)
    })?;

    file.write_all(&data).map_err(|e| {
        error!("❌ 写入文件失败: {}", e);
        format!("写入文件失败: {}", e)
    })?;

    let saved_path = file_path.to_string_lossy().to_string();
    info!("✅ 文件已保存到: {}", saved_path);

    Ok(saved_path)
}

// 实时追加数据到文件
#[tauri::command]
async fn append_data_to_file(data: Vec<u8>, filename: String) -> Result<String, String> {
    use std::fs::OpenOptions;
    use std::io::Write;

    // 获取用户下载目录
    let downloads_dir = dirs::download_dir()
        .ok_or_else(|| "无法获取下载目录".to_string())?;

    let file_path = downloads_dir.join(&filename);

    let mut file = OpenOptions::new()
        .create(true)
        .append(true)
        .open(&file_path)
        .map_err(|e| {
            error!("❌ 打开文件失败: {}", e);
            format!("打开文件失败: {}", e)
        })?;

    file.write_all(&data).map_err(|e| {
        error!("❌ 追加数据失败: {}", e);
        format!("追加数据失败: {}", e)
    })?;

    file.flush().map_err(|e| {
        error!("❌ 刷新文件失败: {}", e);
        format!("刷新文件失败: {}", e)
    })?;

    let saved_path = file_path.to_string_lossy().to_string();
    debug!("✅ 数据已追加到文件: {} ({} 字节)", saved_path, data.len());

    Ok(saved_path)
}

// 格式化字节数据为十六进制字符串
#[tauri::command]
fn format_bytes_hex(data: Vec<u8>) -> String {
    data.iter()
        .map(|b| format!("{:02X}", b))
        .collect::<Vec<_>>()
        .join(" ")
}

// 获取下载文件夹路径
#[tauri::command]
fn get_download_path() -> Result<String, String> {
    let downloads_dir = dirs::download_dir()
        .ok_or_else(|| "无法获取下载目录".to_string())?;

    Ok(downloads_dir.to_string_lossy().to_string())
}

// 初始化下载文件
#[tauri::command]
async fn initialize_download_file(filename: String) -> Result<String, String> {
    use std::fs::File;

    // 获取用户下载目录
    let downloads_dir = dirs::download_dir()
        .ok_or_else(|| "无法获取下载目录".to_string())?;

    let file_path = downloads_dir.join(&filename);

    // 创建空文件
    File::create(&file_path).map_err(|e| {
        error!("❌ 创建下载文件失败: {}", e);
        format!("创建下载文件失败: {}", e)
    })?;

    let full_path = file_path.to_string_lossy().to_string();
    info!("✅ 下载文件已初始化: {}", full_path);

    Ok(full_path)
}

// 分析接收到的数据类型
#[tauri::command]
fn analyze_received_data(data: Vec<u8>) -> String {
    if data.is_empty() {
        return "空数据".to_string();
    }

    // 检查是否是Flash响应
    if data.len() > 5 && data[0] == 0x77 && data[1] == 0x08 {
        return format!("Flash读取响应 ({} 字节)", data.len());
    }

    // 检查是否是测试响应
    if data.len() > 5 && data[0] == 0xFF {
        return format!("测试响应 ({} 字节)", data.len());
    }

    // 检查是否包含文本数据
    let text_bytes = data.iter().filter(|&&b| b >= 0x20 && b <= 0x7E).count();
    let text_percentage = (text_bytes as f32 / data.len() as f32) * 100.0;

    if text_percentage > 50.0 {
        // 尝试解码为UTF-8文本
        if let Ok(text) = String::from_utf8(data.clone()) {
            let preview = if text.len() > 50 {
                format!("{}...", &text[..50])
            } else {
                text
            };
            return format!("文本数据: \"{}\" ({} 字节)", preview, data.len());
        }
    }

    // 检查数据模式
    let zero_count = data.iter().filter(|&&b| b == 0x00).count();
    let ff_count = data.iter().filter(|&&b| b == 0xFF).count();

    if zero_count > data.len() * 9 / 10 {
        return format!("空数据块 ({} 字节, {}% 为0x00)", data.len(), (zero_count * 100) / data.len());
    }

    if ff_count > data.len() * 9 / 10 {
        return format!("未编程Flash块 ({} 字节, {}% 为0xFF)", data.len(), (ff_count * 100) / data.len());
    }

    format!("二进制数据 ({} 字节)", data.len())
}

// 简化的数据收集器
use std::sync::{Mutex, LazyLock};

static DATA_COLLECTOR: LazyLock<Mutex<DataCollector>> = LazyLock::new(|| Mutex::new(DataCollector::new()));

#[derive(Debug)]
struct DataCollector {
    is_collecting: bool,
    packet_count: u32,
    total_bytes: u64,
    file_path: Option<std::path::PathBuf>,
    file_handle: Option<std::fs::File>,
}

impl DataCollector {
    fn new() -> Self {
        Self {
            is_collecting: false,
            packet_count: 0,
            total_bytes: 0,
            file_path: None,
            file_handle: None,
        }
    }

    fn start_collection(&mut self, file_path: std::path::PathBuf) -> Result<(), String> {
        use std::fs::File;

        // 创建文件
        let file = File::create(&file_path).map_err(|e| {
            error!("❌ 创建文件失败: {}", e);
            format!("创建文件失败: {}", e)
        })?;

        self.is_collecting = true;
        self.packet_count = 0;
        self.total_bytes = 0;
        self.file_path = Some(file_path.clone());
        self.file_handle = Some(file);

        info!("✅ 数据收集器已启动，文件已创建: {:?}", file_path);
        Ok(())
    }

    fn add_data(&mut self, data: Vec<u8>) -> Result<String, String> {
        if !self.is_collecting {
            return Err("数据收集器未启动".to_string());
        }

        // 实时写入文件
        if let Some(ref mut file) = self.file_handle {
            use std::io::Write;
            file.write_all(&data).map_err(|e| {
                error!("❌ 实时写入数据失败: {}", e);
                format!("实时写入数据失败: {}", e)
            })?;

            // 定期刷新文件缓冲区
            if self.packet_count % 100 == 0 {
                file.flush().map_err(|e| {
                    error!("❌ 刷新文件缓冲区失败: {}", e);
                    format!("刷新文件缓冲区失败: {}", e)
                })?;
            }
        } else {
            return Err("文件句柄未初始化".to_string());
        }

        self.total_bytes += data.len() as u64;
        self.packet_count += 1;

        // 每100个包时记录一次
        if self.packet_count % 100 == 0 || self.packet_count <= 5 {
            info!("📦 已收集 {} 个数据包，总计 {} 字节", self.packet_count, self.total_bytes);
        }

        Ok(format!("包#{}, 总计{}字节", self.packet_count, self.total_bytes))
    }

    fn finish_and_save(&mut self) -> Result<String, String> {
        if !self.is_collecting {
            return Err("数据收集器未启动".to_string());
        }

        // 最终刷新文件缓冲区
        if let Some(ref mut file) = self.file_handle {
            use std::io::Write;
            file.flush().map_err(|e| {
                error!("❌ 最终刷新文件失败: {}", e);
                format!("最终刷新文件失败: {}", e)
            })?;
        }

        let result = format!("下载完成: {} 包, {} 字节", self.packet_count, self.total_bytes);
        info!("✅ {}", result);

        // 重置状态
        self.is_collecting = false;
        self.packet_count = 0;
        self.total_bytes = 0;
        self.file_path = None;
        self.file_handle = None;

        Ok(result)
    }

    fn get_stats(&self) -> String {
        format!("包数: {}, 总字节: {}, 收集中: {}",
                self.packet_count, self.total_bytes, self.is_collecting)
    }
}

// 开始数据收集
#[tauri::command]
async fn start_data_collection(filename: String) -> Result<String, String> {
    // 获取用户下载目录
    let downloads_dir = dirs::download_dir()
        .ok_or_else(|| "无法获取下载目录".to_string())?;

    let file_path = downloads_dir.join(&filename);
    let full_path = file_path.to_string_lossy().to_string();

    // 启动数据收集器
    let mut collector = DATA_COLLECTOR.lock().unwrap();
    collector.start_collection(file_path)?; // 处理可能的错误

    Ok(full_path)
}

// 添加数据到收集器
#[tauri::command]
async fn add_data_to_collector(data: Vec<u8>) -> Result<String, String> {
    let mut collector = DATA_COLLECTOR.lock().unwrap();
    collector.add_data(data)
}

// 获取收集器统计
#[tauri::command]
fn get_collector_stats() -> Result<String, String> {
    let collector = DATA_COLLECTOR.lock().unwrap();
    Ok(collector.get_stats())
}

// 完成数据收集并保存文件
#[tauri::command]
fn finish_data_collection() -> Result<String, String> {
    let mut collector = DATA_COLLECTOR.lock().unwrap();
    collector.finish_and_save()
}

// 数据包计数器
use std::sync::atomic::{AtomicU64, Ordering};
static PACKET_COUNTER: AtomicU64 = AtomicU64::new(0);

// 处理接收到的数据并打印日志
#[tauri::command]
fn log_received_data(data: Vec<u8>) -> String {
    use std::time::{SystemTime, UNIX_EPOCH};

    // 增加数据包计数
    let packet_count = PACKET_COUNTER.fetch_add(1, Ordering::Relaxed) + 1;

    // 简单的时间戳（秒数）
    let timestamp = SystemTime::now()
        .duration_since(UNIX_EPOCH)
        .unwrap()
        .as_secs();

    let hex_string = data.iter()
        .map(|b| format!("{:02X}", b))
        .collect::<Vec<_>>()
        .join("-");

    let log_message = format!("[{}] 📥 收到数据 #{} ({}字节): {}", timestamp, packet_count, data.len(), hex_string);

    // 只在每1000个数据包或前5个数据包时打印到终端
    if packet_count % 1000 == 0 || packet_count <= 5 {
        info!("{}", log_message);
    }

    // 始终返回格式化的消息给前端显示（前端可以选择是否显示）
    log_message
}

// 重置数据包计数器
#[tauri::command]
fn reset_packet_counter() {
    PACKET_COUNTER.store(0, Ordering::Relaxed);
    info!("📊 数据包计数器已重置");
}

#[cfg_attr(mobile, tauri::mobile_entry_point)]
pub fn run() {
    // 初始化日志
    env_logger::Builder::from_default_env()
        .filter_level(log::LevelFilter::Debug)
        .init();

    info!("🚀 启动串口测试工具");

    tauri::Builder::default()
        .plugin(tauri_plugin_opener::init())
        .plugin(tauri_plugin_serialplugin::init())
        .invoke_handler(tauri::generate_handler![
            debug_serial_connection,
            build_flash_read_command,
            parse_flash_response,
            verify_crc,
            validate_flash_data,
            generate_data_summary,
            save_data_to_file,
            append_data_to_file,
            format_bytes_hex,
            get_download_path,
            analyze_received_data,
            start_data_collection,
            add_data_to_collector,
            get_collector_stats,
            finish_data_collection,
            log_received_data,
            reset_packet_counter
        ])
        .run(tauri::generate_context!())
        .expect("error while running tauri application");
}
