[package]
name = "serialporttest"
version = "0.1.0"
description = "串口测试工具 - 基于Tauri和React的串口通信测试应用"
authors = ["Tartan"]
edition = "2021"
license = "MIT"
repository = "https://github.com/tartan/serialporttest"

# See more keys and their definitions at https://doc.rust-lang.org/cargo/reference/manifest.html

[lib]
# The `_lib` suffix may seem redundant but it is necessary
# to make the lib name unique and wouldn't conflict with the bin name.
# This seems to be only an issue on Windows, see https://github.com/rust-lang/cargo/issues/8519
name = "serialporttest_lib"
crate-type = ["staticlib", "cdylib", "rlib"]

[build-dependencies]
tauri-build = { version = "2", features = [] }

[dependencies]
tauri = { version = "2", features = [] }
tauri-plugin-opener = "2"
serde = { version = "1", features = ["derive"] }
serde_json = "1"
tauri-plugin-serialplugin = "2.17.1"
log = "0.4"
env_logger = "0.10"
serialport = "4.2"
dirs = "5.0"

