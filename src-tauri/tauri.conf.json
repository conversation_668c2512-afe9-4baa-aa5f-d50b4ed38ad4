{"$schema": "https://schema.tauri.app/config/2", "productName": "串口测试工具", "version": "0.1.0", "identifier": "com.tartan.serialport", "build": {"beforeDevCommand": "bun run dev", "devUrl": "http://localhost:1420", "beforeBuildCommand": "bun run build", "frontendDist": "../dist"}, "app": {"windows": [{"title": "串口测试工具", "width": 1200, "height": 800, "minWidth": 800, "minHeight": 600, "resizable": true, "maximizable": true, "minimizable": true, "closable": true, "center": true}], "security": {"csp": null}}, "bundle": {"active": true, "targets": "all", "icon": ["icons/32x32.png", "icons/128x128.png", "icons/<EMAIL>", "icons/icon.icns", "icons/icon.ico"], "windows": {"certificateThumbprint": null, "digestAlgorithm": "sha256", "timestampUrl": ""}, "longDescription": "一个基于Tauri和React的串口通信测试工具，支持串口枚举、配置和数据收发。", "shortDescription": "串口测试工具"}}