import React, { useState, useEffect, useRef } from 'react';
import { SerialPort, DataBits, StopBits, Parity } from 'tauri-plugin-serialplugin';
import { invoke } from '@tauri-apps/api/core';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { SerialConfig } from '@/types/serial';

export const SerialPortManager: React.FC = () => {
  const [availablePorts, setAvailablePorts] = useState<{ [key: string]: any }>({});
  const [selectedPort, setSelectedPort] = useState<string>('');
  const [isConnected, setIsConnected] = useState(false);
  const [serialPort, setSerialPort] = useState<SerialPort | null>(null);
  const [receivedData, setReceivedData] = useState<string[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [connectionStatus, setConnectionStatus] = useState<string>('未连接');
  const [lastError, setLastError] = useState<string>('');

  // 用于自动滚动的ref
  const textareaRef = useRef<HTMLTextAreaElement>(null);

  // 串口配置
  const [config, setConfig] = useState<SerialConfig>({
    path: '',
    baudRate: 115200,
    dataBits: DataBits.Eight,
    stopBits: StopBits.One,
    parity: Parity.None,
  });

  // 获取可用串口
  const refreshPorts = async () => {
    try {
      setIsLoading(true);
      const ports = await SerialPort.available_ports();
      setAvailablePorts(ports);
      console.log('Available ports:', ports);
    } catch (error) {
      console.error('Failed to get available ports:', error);
    } finally {
      setIsLoading(false);
    }
  };

  // 连接串口
  const connectPort = async () => {
    if (!selectedPort) return;

    try {
      setIsLoading(true);
      setConnectionStatus('正在连接...');
      setLastError('');

      const timestamp = new Date().toLocaleTimeString();
      setReceivedData(prev => [...prev, `[${timestamp}] 尝试连接到 ${selectedPort}`]);

      // 调用Rust后端调试命令
      try {
        const debugResult = await invoke<string>('debug_serial_connection', {
          portPath: selectedPort,
          baudRate: config.baudRate
        });
        setReceivedData(prev => [...prev, `[${timestamp}] 🔍 ${debugResult}`]);
      } catch (debugError) {
        setReceivedData(prev => [...prev, `[${timestamp}] ⚠️ 调试信息获取失败: ${debugError}`]);
      }

      const port = new SerialPort({
        path: selectedPort,
        baudRate: config.baudRate,
        dataBits: config.dataBits,
        stopBits: config.stopBits,
        parity: config.parity,
      });

      setReceivedData(prev => [...prev, `[${timestamp}] 🔌 开始使用Tauri串口插件连接...`]);

      try {
        await port.open();
        setReceivedData(prev => [...prev, `[${timestamp}] ✅ Tauri串口插件打开成功`]);
      } catch (openError) {
        setReceivedData(prev => [...prev, `[${timestamp}] ❌ Tauri串口插件打开失败: ${openError}`]);
        throw openError;
      }

      try {
        await port.startListening();
        setReceivedData(prev => [...prev, `[${timestamp}] ✅ 开始监听数据成功`]);
      } catch (listenError) {
        setReceivedData(prev => [...prev, `[${timestamp}] ❌ 开始监听失败: ${listenError}`]);
        throw listenError;
      }

      // 设置数据监听器
      try {
        await port.listen(async (data) => {
          // 确保接收原始字节数据，不进行字符串解码
          const bytes = data instanceof Uint8Array ? data : new Uint8Array(data as ArrayBuffer);

          // 使用后端日志函数处理数据（后端会控制打印频率）
          try {
            const logMessage = await invoke<string>('log_received_data', {
              data: Array.from(bytes)
            });

            // 检查是否应该显示这条消息（每1000个或前5个数据包）
            const shouldShow = logMessage.match(/#(\d+)/);
            if (shouldShow) {
              const packetNum = parseInt(shouldShow[1]);
              if (packetNum <= 5 || packetNum % 1000 === 0) {
                setReceivedData(prev => [...prev, logMessage]);
              }
            }
          } catch (logError) {
            // 如果后端日志失败，使用前端备用方案
            const timestamp = new Date().toLocaleTimeString();
            const dataStr = Array.from(bytes).map(b => b.toString(16).padStart(2, '0')).join('-').toUpperCase();
            setReceivedData(prev => [...prev, `[${timestamp}] 📥 收到数据 (${bytes.length}字节): ${dataStr}`]);
          }

          // 尝试保存数据到收集器（如果收集器已启动）
          try {
            // 使用新的数据收集器保存数据
            const saveResult = await invoke<string>('add_data_to_collector', {
              data: Array.from(bytes)
            });

            // 累积所有数据到downloadedData用于统计
            setDownloadedData(prev => [...prev, bytes]);

            // 定期报告保存状态（每100个数据包）
            setDownloadedDataCount(prev => {
              const newCount = prev + 1;
              if (newCount % 100 === 0 || newCount <= 5) {
                const saveTimestamp = new Date().toLocaleTimeString();
                setReceivedData(prevData => [...prevData, `[${saveTimestamp}] 💾 已保存到数据收集器: ${saveResult}`]);
              }
              return newCount;
            });

          } catch (saveError) {
            // 显示保存错误信息，帮助调试
            const errorTimestamp = new Date().toLocaleTimeString();
            setReceivedData(prev => [...prev, `[${errorTimestamp}] ⚠️ 数据收集器保存失败: ${saveError}`]);
          }
        }, false); // 设置 decode=false 来接收原始字节数据
        setReceivedData(prev => [...prev, `[${timestamp}] ✅ 数据监听器设置成功`]);
      } catch (listenSetupError) {
        setReceivedData(prev => [...prev, `[${timestamp}] ❌ 数据监听器设置失败: ${listenSetupError}`]);
        throw listenSetupError;
      }

      setSerialPort(port);
      setIsConnected(true);
      setConnectionStatus(`已连接到 ${selectedPort}`);

      const connectTime = new Date().toLocaleTimeString();
      setReceivedData(prev => [...prev, `[${connectTime}] ✅ 连接成功！配置: ${config.baudRate}bps, ${config.dataBits}N${config.stopBits}, ${config.parity}`]);

      // 发送连接测试信号
      setTimeout(async () => {
        try {
          const testTime = new Date().toLocaleTimeString();
          setReceivedData(prev => [...prev, `[${testTime}] 📡 发送连接测试信号...`]);

          // 发送一个简单的测试字节
          const testData = new Uint8Array([0x00]);
          await port.writeBinary(testData);

          setReceivedData(prev => [...prev, `[${testTime}] 📤 测试信号已发送 (00)`]);
        } catch (error) {
          const testTime = new Date().toLocaleTimeString();
          setReceivedData(prev => [...prev, `[${testTime}] ⚠️ 测试信号发送失败，但连接可能正常`]);
        }
      }, 1000);

      console.log('Connected to port:', selectedPort);
    } catch (error) {
      const errorMsg = error instanceof Error ? error.message : '未知错误';
      setLastError(`连接失败: ${errorMsg}`);
      setConnectionStatus('连接失败');

      const timestamp = new Date().toLocaleTimeString();
      setReceivedData(prev => [...prev, `[${timestamp}] ❌ 连接失败: ${errorMsg}`]);

      console.error('Failed to connect to port:', error);
    } finally {
      setIsLoading(false);
    }
  };

  // 断开串口
  const disconnectPort = async () => {
    if (!serialPort) return;

    try {
      setIsLoading(true);
      setConnectionStatus('正在断开...');

      const timestamp = new Date().toLocaleTimeString();
      setReceivedData(prev => [...prev, `[${timestamp}] 正在断开连接...`]);

      await serialPort.stopListening();
      setReceivedData(prev => [...prev, `[${timestamp}] 停止监听数据`]);

      await serialPort.close();
      setReceivedData(prev => [...prev, `[${timestamp}] 串口已关闭`]);

      setSerialPort(null);
      setIsConnected(false);
      setConnectionStatus('未连接');
      setLastError('');

      const disconnectTime = new Date().toLocaleTimeString();
      setReceivedData(prev => [...prev, `[${disconnectTime}] 🔌 已断开连接`]);

      console.log('Disconnected from port');
    } catch (error) {
      const errorMsg = error instanceof Error ? error.message : '未知错误';
      setLastError(`断开失败: ${errorMsg}`);
      setConnectionStatus('断开失败');

      const timestamp = new Date().toLocaleTimeString();
      setReceivedData(prev => [...prev, `[${timestamp}] ❌ 断开失败: ${errorMsg}`]);

      console.error('Failed to disconnect from port:', error);
    } finally {
      setIsLoading(false);
    }
  };

  // 发送指定的字节数组
  const sendTestData = async () => {
    if (!serialPort || !isConnected) return;

    try {
      // 发送指定的字节数组: 77-22-02-00-01-05-8E
      const testData = new Uint8Array([0x77, 0x22, 0x02, 0x00, 0x01, 0x05, 0x8E]);
      await serialPort.writeBinary(testData);
      
      const timestamp = new Date().toLocaleTimeString();
      const dataStr = Array.from(testData).map(b => b.toString(16).padStart(2, '0')).join('-').toUpperCase();
      setReceivedData(prev => [...prev, `[${timestamp}] SENT: ${dataStr}`]);
      
      console.log('Sent test data:', dataStr);
    } catch (error) {
      console.error('Failed to send data:', error);
    }
  };

  // 清空接收数据
  const clearReceivedData = () => {
    setReceivedData([]);
  };

  // 下载状态
  const [isDownloading, setIsDownloading] = useState(false);
  const [downloadProgress, setDownloadProgress] = useState(0);
  const [currentBlock, setCurrentBlock] = useState(0);
  const [totalBlocks, setTotalBlocks] = useState(0);
  const [startTime, setStartTime] = useState<number>(0);



  // 存储下载的数据
  const [downloadedData, setDownloadedData] = useState<Uint8Array[]>([]);

  // 数据包计数器
  const [downloadedDataCount, setDownloadedDataCount] = useState<number>(0);

  // 实时保存文件名
  const [realtimeFilename, setRealtimeFilename] = useState<string>('');

  // 数据收集状态
  const [isCollecting, setIsCollecting] = useState<boolean>(false);



  // 注意：进度条现在使用独立的UI组件显示，不再在日志中显示

  // 进度条组件
  const ProgressBar = () => {
    if (!isDownloading) return null;

    const percentage = Math.round((currentBlock / totalBlocks) * 100);
    const barLength = 40;
    const filledLength = Math.round((currentBlock / totalBlocks) * barLength);
    const emptyLength = barLength - filledLength;

    const filledBar = '█'.repeat(filledLength);
    const emptyBar = '░'.repeat(emptyLength);

    // 计算下载速度和剩余时间
    const elapsed = (Date.now() - startTime) / 1000; // 秒
    const bytesDownloaded = currentBlock * 256; // 假设每块256字节
    const speed = elapsed > 0 ? Math.round(bytesDownloaded / elapsed) : 0;
    const remainingBlocks = totalBlocks - currentBlock;
    const eta = speed > 0 ? Math.round((remainingBlocks * 256) / speed) : 0;

    return (
      <div className="bg-gray-50 border rounded-lg p-4 mb-4">
        <div className="flex items-center justify-between mb-2">
          <span className="text-sm font-medium text-gray-700">Flash数据下载进度</span>
          <span className="text-sm text-gray-500">{percentage}%</span>
        </div>

        <div className="w-full bg-gray-200 rounded-full h-2.5 mb-2">
          <div
            className="bg-blue-600 h-2.5 rounded-full transition-all duration-300"
            style={{ width: `${percentage}%` }}
          ></div>
        </div>

        <div className="flex justify-between text-xs text-gray-500">
          <span>块: {currentBlock}/{totalBlocks}</span>
          <span>速度: {speed > 0 ? `${(speed/1024).toFixed(1)} KB/s` : '--'}</span>
          <span>剩余: {eta > 0 ? `${eta}s` : '--'}</span>
        </div>

        <div className="mt-2 font-mono text-xs text-gray-600">
          [{filledBar}{emptyBar}]
        </div>
      </div>
    );
  };

  // 下载Flash数据
  const downloadFlashData = async () => {
    if (!serialPort || !isConnected) {
      alert('请先连接串口');
      return;
    }

    try {
      setIsDownloading(true);
      setDownloadProgress(0);
      setDownloadedData([]);
      setDownloadedDataCount(0);
      setCurrentBlock(0);
      setStartTime(Date.now());

      const timestamp = new Date().toLocaleTimeString();
      setReceivedData(prev => [...prev, `[${timestamp}] 🔽 开始下载Flash数据...`]);

      // 重置数据包计数器
      try {
        await invoke('reset_packet_counter');
      } catch (error) {
        console.warn('重置计数器失败:', error);
      }

      // 启动数据收集
      const now = new Date();
      const filename = `flash_data_realtime_${now.getFullYear()}${(now.getMonth()+1).toString().padStart(2,'0')}${now.getDate().toString().padStart(2,'0')}_${now.getHours().toString().padStart(2,'0')}${now.getMinutes().toString().padStart(2,'0')}.bin`;

      try {
        // 启动后端数据收集器
        const filePath = await invoke<string>('start_data_collection', {
          filename: filename
        });

        setIsCollecting(true);
        setRealtimeFilename(filename);

        setReceivedData(prev => [...prev, `[${timestamp}] 📁 数据收集器已启动`]);
        setReceivedData(prev => [...prev, `[${timestamp}] 📄 文件保存路径: ${filePath}`]);
      } catch (collectorError) {
        setReceivedData(prev => [...prev, `[${timestamp}] ❌ 启动数据收集器失败: ${collectorError}`]);
        throw new Error(`启动数据收集器失败: ${collectorError}`);
      }

      // 下载参数 - 修改为更保守的设置
      const startAddr = 0x10000;   // 起始地址 (64KB位置)
      const endAddr = 0x800000;    // 结束地址 (8MB位置) - MaxAddr
      const blockSize = 256;
      const totalSize = endAddr - startAddr;

      setReceivedData(prev => [...prev, `[${timestamp}] 📊 下载参数: 起始地址=0x${startAddr.toString(16).toUpperCase()}, 结束地址=0x${endAddr.toString(16).toUpperCase()}, 块大小=${blockSize}`]);

      let currentAddr = startAddr;
      let completedBlocks = 0;
      const totalBlocks = Math.ceil(totalSize / blockSize);
      setTotalBlocks(totalBlocks);

      while (currentAddr < endAddr) {
        const remainingBytes = endAddr - currentAddr;
        const readLength = Math.min(blockSize, remainingBytes);

        // 使用Rust后端构建Flash读取命令（包含发送前校验）
        try {
          const command = await invoke<number[]>('build_flash_read_command', {
            addr: currentAddr,
            len: readLength
          });

          const commandBytes = new Uint8Array(command);

          // 只在每500个块或前3个块时打印日志，进一步减少UI更新
          if (completedBlocks % 500 === 0 || completedBlocks < 3) {
            const cmdTimestamp = new Date().toLocaleTimeString();
            setReceivedData(prev => [...prev, `[${cmdTimestamp}] 📤 发送命令块 ${completedBlocks+1}/${totalBlocks} (地址: 0x${currentAddr.toString(16).toUpperCase()})`]);
          }

          // 发送命令
          await serialPort.writeBinary(commandBytes);

          // 等待响应 - 这是关键！每个命令都需要等待响应
          // Flash读取需要时间，特别是大块数据
          await new Promise(resolve => setTimeout(resolve, 10));

        } catch (commandError) {
          const errorTimestamp = new Date().toLocaleTimeString();
          setReceivedData(prev => [...prev, `[${errorTimestamp}] ❌ Flash命令构建失败: ${commandError} (地址: 0x${currentAddr.toString(16).toUpperCase()})`]);
          throw new Error(`Flash命令构建失败: ${commandError}`);
        }

        currentAddr += readLength;
        completedBlocks++;

        // 更新进度状态
        const progress = Math.round((completedBlocks / totalBlocks) * 100);
        setDownloadProgress(progress);
        setCurrentBlock(completedBlocks);

        // 不再在日志中显示进度条，只在UI组件中显示
      }

      // 等待所有响应完成
      const waitTimestamp = new Date().toLocaleTimeString();
      setReceivedData(prev => [...prev, `[${waitTimestamp}] ⏳ 等待所有响应完成...`]);

      // 等待最多10秒收集响应
      await new Promise(resolve => setTimeout(resolve, 10000));

      // 检查下载结果
      const finalTimestamp = new Date().toLocaleTimeString();
      const totalDownloaded = downloadedData.reduce((sum, chunk) => sum + chunk.length, 0);
      setReceivedData(prev => [...prev, `[${finalTimestamp}] ✅ 下载完成！前端统计: ${downloadedDataCount} 个数据包，总计 ${totalDownloaded} 字节数据`]);

      if (totalDownloaded > 0) {
        // 合并所有数据块
        const combinedData = new Uint8Array(totalDownloaded);
        let offset = 0;
        for (const chunk of downloadedData) {
          combinedData.set(chunk, offset);
          offset += chunk.length;
        }

        // 验证数据完整性
        const validationTimestamp = new Date().toLocaleTimeString();
        setReceivedData(prev => [...prev, `[${validationTimestamp}] 🔍 正在验证Flash数据完整性...`]);

        try {
          // 调用Rust后端验证数据
          const validationReport = await invoke<string>('validate_flash_data', {
            data: Array.from(combinedData)
          });

          const reportTimestamp = new Date().toLocaleTimeString();
          setReceivedData(prev => [...prev, `[${reportTimestamp}] 📋 数据验证报告:`]);

          // 将验证报告按行添加到日志中
          const reportLines = validationReport.split('\n');
          reportLines.forEach(line => {
            if (line.trim()) {
              setReceivedData(prev => [...prev, `[${reportTimestamp}]   ${line}`]);
            }
          });

          // 生成数据摘要
          const summary = await invoke<string>('generate_data_summary', {
            data: Array.from(combinedData),
            startAddr: startAddr
          });

          const summaryTimestamp = new Date().toLocaleTimeString();
          setReceivedData(prev => [...prev, `[${summaryTimestamp}] 📊 数据摘要:`]);

          const summaryLines = summary.split('\n');
          summaryLines.forEach(line => {
            if (line.trim()) {
              setReceivedData(prev => [...prev, `[${summaryTimestamp}]   ${line}`]);
            }
          });

        } catch (validationError) {
          const errorTimestamp = new Date().toLocaleTimeString();
          setReceivedData(prev => [...prev, `[${errorTimestamp}] ⚠️ 数据验证失败: ${validationError}`]);
        }

        // 保存文件
        const blob = new Blob([combinedData], { type: 'application/octet-stream' });
        const url = URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;

        const now = new Date();
        const filename = `flash_data_${now.getFullYear()}${(now.getMonth()+1).toString().padStart(2,'0')}${now.getDate().toString().padStart(2,'0')}_${now.getHours().toString().padStart(2,'0')}${now.getMinutes().toString().padStart(2,'0')}.bin`;
        link.download = filename;

        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        URL.revokeObjectURL(url);

        const saveTimestamp = new Date().toLocaleTimeString();
        setReceivedData(prev => [...prev, `[${saveTimestamp}] 💾 数据已保存到文件: ${filename}`]);

        alert(`下载完成！\n总计下载: ${totalDownloaded} 字节\n文件已保存: ${filename}\n\n请查看日志中的数据验证报告以确认数据完整性。`);
      } else {
        alert('下载失败：未收到任何数据');
      }

    } catch (error) {
      const errorMsg = error instanceof Error ? error.message : String(error);
      const errorTimestamp = new Date().toLocaleTimeString();
      setReceivedData(prev => [...prev, `[${errorTimestamp}] ❌ 下载失败: ${errorMsg}`]);
      console.error('Download failed:', error);
      alert(`下载失败: ${errorMsg}`);
    } finally {
      // 在清理状态之前，完成数据收集并获取统计
      const finalTimestamp = new Date().toLocaleTimeString();
      console.log(`[DEBUG] Finally块: isCollecting=${isCollecting}`);

      if (isCollecting) {
        try {
          // 完成数据收集并保存文件
          const backendStats = await invoke<string>('finish_data_collection');
          setReceivedData(prev => [...prev, `[${finalTimestamp}] 📊 后端统计: ${backendStats}`]);
        } catch (statsError) {
          setReceivedData(prev => [...prev, `[${finalTimestamp}] ⚠️ 完成数据收集失败: ${statsError}`]);
        }
      } else {
        setReceivedData(prev => [...prev, `[${finalTimestamp}] ⚠️ 数据收集器未启动，无法获取统计`]);
      }

      // 清理状态
      setIsDownloading(false);
      setDownloadProgress(0);
      setCurrentBlock(0);
      setTotalBlocks(0);
      setDownloadedDataCount(0);
      setIsCollecting(false); // 清理收集状态
      setRealtimeFilename(''); // 清理实时文件名
    }
  };



  // 组件挂载时获取串口列表
  useEffect(() => {
    refreshPorts();
  }, []);

  // 自动滚动到底部
  useEffect(() => {
    if (textareaRef.current) {
      textareaRef.current.scrollTop = textareaRef.current.scrollHeight;
    }
  }, [receivedData]);

  return (
    <div className="space-y-6">
      {/* 串口选择和连接 */}
      <Card>
        <CardHeader>
          <CardTitle>串口连接</CardTitle>
          <CardDescription>选择并配置串口连接</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex gap-4 items-end">
            <div className="flex-1">
              <Label htmlFor="port-select">选择串口</Label>
              <Select value={selectedPort} onValueChange={setSelectedPort} disabled={isConnected}>
                <SelectTrigger id="port-select">
                  <SelectValue placeholder="选择一个串口" />
                </SelectTrigger>
                <SelectContent>
                  {Object.entries(availablePorts).map(([portName, portInfo]) => (
                    <SelectItem key={portName} value={portName}>
                      {portName} {portInfo.product && `(${portInfo.product})`}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <Button onClick={refreshPorts} disabled={isLoading || isConnected} variant="outline">
              刷新
            </Button>
          </div>

          {/* 串口配置 */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div>
              <Label htmlFor="baud-rate">波特率</Label>
              <Select 
                value={config.baudRate.toString()} 
                onValueChange={(value) => setConfig(prev => ({ ...prev, baudRate: parseInt(value) }))}
                disabled={isConnected}
              >
                <SelectTrigger id="baud-rate">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="9600">9600</SelectItem>
                  <SelectItem value="19200">19200</SelectItem>
                  <SelectItem value="38400">38400</SelectItem>
                  <SelectItem value="57600">57600</SelectItem>
                  <SelectItem value="115200">115200</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div>
              <Label htmlFor="data-bits">数据位</Label>
              <Select
                value={config.dataBits}
                onValueChange={(value) => setConfig(prev => ({ ...prev, dataBits: value as DataBits }))}
                disabled={isConnected}
              >
                <SelectTrigger id="data-bits">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value={DataBits.Eight}>8</SelectItem>
                  <SelectItem value={DataBits.Seven}>7</SelectItem>
                  <SelectItem value={DataBits.Six}>6</SelectItem>
                  <SelectItem value={DataBits.Five}>5</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div>
              <Label htmlFor="stop-bits">停止位</Label>
              <Select
                value={config.stopBits}
                onValueChange={(value) => setConfig(prev => ({ ...prev, stopBits: value as StopBits }))}
                disabled={isConnected}
              >
                <SelectTrigger id="stop-bits">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value={StopBits.One}>1</SelectItem>
                  <SelectItem value={StopBits.Two}>2</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div>
              <Label htmlFor="parity">校验位</Label>
              <Select 
                value={config.parity} 
                onValueChange={(value) => setConfig(prev => ({ ...prev, parity: value as Parity }))}
                disabled={isConnected}
              >
                <SelectTrigger id="parity">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="None">None</SelectItem>
                  <SelectItem value="Odd">Odd</SelectItem>
                  <SelectItem value="Even">Even</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          <div className="flex gap-2 items-center flex-wrap">
            {!isConnected ? (
              <Button
                onClick={connectPort}
                disabled={!selectedPort || isLoading}
                className="bg-green-600 hover:bg-green-700"
              >
                {isLoading ? '连接中...' : '连接'}
              </Button>
            ) : (
              <Button
                onClick={disconnectPort}
                disabled={isLoading}
                variant="destructive"
              >
                {isLoading ? '断开中...' : '断开'}
              </Button>
            )}

            <Badge variant={isConnected ? "default" : "secondary"}>
              {connectionStatus}
            </Badge>

            {lastError && (
              <Badge variant="destructive" className="text-xs">
                {lastError}
              </Badge>
            )}
          </div>
        </CardContent>
      </Card>

      {/* 数据发送和接收 */}
      <Card>
        <CardHeader>
          <CardTitle>数据传输</CardTitle>
          <CardDescription>发送测试数据并查看响应</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex gap-2">
            <Button
              onClick={sendTestData}
              disabled={!isConnected || isLoading}
              className="bg-blue-600 hover:bg-blue-700"
            >
              发送测试数据 (77-22-02-00-01-05-8E)
            </Button>
            <Button
              onClick={downloadFlashData}
              disabled={isDownloading || isLoading}
              className="bg-green-600 hover:bg-green-700"
            >
              {isDownloading ? `下载中... ${downloadProgress}%` : '下载Flash数据'}
            </Button>
            <Button onClick={clearReceivedData} variant="outline">
              清空数据
            </Button>
          </div>

          {/* 进度条组件 */}
          <ProgressBar />

          {/* 实时保存状态 */}
          {realtimeFilename && (
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-3">
              <div className="flex items-center gap-2">
                <div className="w-2 h-2 bg-blue-500 rounded-full animate-pulse"></div>
                <span className="text-sm font-medium text-blue-700">实时保存中</span>
              </div>
              <p className="text-xs text-blue-600 mt-1">文件: {realtimeFilename}</p>
              <p className="text-xs text-blue-500">二进制数据自动保存到下载文件夹，可用十六进制编辑器打开</p>
            </div>
          )}

          <div>
            <Label htmlFor="received-data">接收到的数据</Label>
            <Textarea
              ref={textareaRef}
              id="received-data"
              value={receivedData.join('\n')}
              readOnly
              className="h-40 font-mono text-sm"
              placeholder="这里将显示接收到的数据..."
            />
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
