# 新架构测试指南

## 测试环境准备

### 1. 启动应用
```bash
# 开发模式
bun run tauri dev

# 或者构建后测试
bun run tauri build
```

### 2. 准备串口设备
- 连接您的串口设备
- 确保设备支持Flash读取协议
- 记录设备的串口路径（如 `/dev/tty.usbserial-xxx`）

## 测试步骤

### 1. 基础连接测试
1. 打开应用
2. 点击"刷新"按钮获取可用串口
3. 选择您的串口设备
4. 设置正确的波特率（通常是115200）
5. 点击"连接"按钮
6. 观察连接状态和日志输出

### 2. 新架构下载测试
1. 确保串口已连接
2. 点击"下载Flash数据 (新架构)"按钮
3. 观察以下指标：

#### 进度条显示
- **当前块数/总块数**：应该显示实时进度
- **下载速度**：应该显示KB/s单位的速度
- **剩余时间**：应该显示预估完成时间
- **进度百分比**：应该平滑增长

#### 性能指标
- **UI响应性**：界面应该保持流畅，不卡顿
- **内存使用**：内存使用应该稳定，不会持续增长
- **CPU使用**：CPU使用应该合理，不会100%占用

### 3. 对比测试（如果有旧版本）
如果您有旧版本的应用，可以进行对比测试：

#### Windows平台对比
- **旧架构**：可能出现界面卡顿、速度慢
- **新架构**：应该流畅运行，速度快

#### macOS平台对比
- **旧架构**：相对较好的性能
- **新架构**：性能进一步提升，更精确的进度显示

## 预期结果

### 正常情况
1. **连接成功**：串口连接状态显示"已连接"
2. **下载启动**：点击下载按钮后立即开始
3. **进度更新**：每隔几秒更新一次进度信息
4. **速度稳定**：下载速度应该相对稳定
5. **完成提示**：下载完成后显示成功消息和文件路径

### 文件验证
下载完成后，检查生成的文件：
- **文件位置**：在用户下载目录
- **文件大小**：应该符合预期（约8MB）
- **文件格式**：`.bin`二进制文件

## 故障排除

### 连接问题
- **串口不可用**：检查设备连接和驱动
- **权限问题**：确保应用有串口访问权限
- **波特率错误**：尝试不同的波特率设置

### 下载问题
- **下载中断**：检查串口连接稳定性
- **速度异常**：检查设备响应和数据质量
- **文件损坏**：检查CRC校验和错误日志

### 性能问题
- **界面卡顿**：可能是旧架构，确认使用新版本
- **内存泄漏**：重启应用，观察内存使用趋势
- **CPU过高**：检查后台进程和系统资源

## 日志分析

### 关键日志信息
查看终端输出中的关键信息：
- `🔽 后端Flash下载任务启动...`：下载开始
- `🔌 正在打开串口`：串口初始化
- `正在下载块 X/Y...`：进度更新
- `✅ Flash下载完成`：下载成功

### 错误日志
注意以下错误信息：
- `打开串口失败`：串口连接问题
- `读取响应失败`：通信超时或错误
- `CRC校验失败`：数据传输错误

## 性能基准

### 预期性能指标
- **下载速度**：10-50 KB/s（取决于设备和串口速度）
- **UI更新频率**：每2-3秒更新一次
- **内存使用**：稳定在50-100MB
- **CPU使用**：正常情况下<20%

### Windows vs macOS
新架构下，两个平台的性能应该基本一致：
- **速度差异**：<10%
- **稳定性**：都应该稳定运行
- **用户体验**：界面响应性一致

## 反馈收集

测试时请记录：
1. **操作系统版本**
2. **串口设备型号**
3. **下载速度和时间**
4. **任何错误或异常**
5. **用户体验评价**

这些信息将帮助进一步优化应用性能。
