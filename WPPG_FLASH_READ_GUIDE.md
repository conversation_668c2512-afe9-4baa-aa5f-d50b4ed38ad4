# WPPG Flash 一步到位读取功能使用指南

## 功能概述

新实现的 `wppg_flash_read_region` 函数提供了一个"一步到位"的Flash读取解决方案，前端只需要一次调用即可完成整个Flash区域的读取，无需分散到多次写、读和解析操作。

## 核心特性

### 1. 一步到位的设计
- **前端调用简单**：只需一次 `invoke` 调用
- **后端统一处理**：串口操作、命令发送、数据接收、解析全部在后端完成
- **返回完整数据**：直接返回 `Vec<u8>` 格式的完整Flash数据

### 2. 低延迟串口配置
- **1ms超时设置**：`serialport::new(...).timeout(Duration::from_millis(1))`
- **优化读取缓冲区**：预设 `5 + chunkSize` 字节的读缓冲区
- **阻塞式精确读取**：确保读取完整响应帧

### 3. 循环读取机制
每次循环执行以下步骤：
1. 调用 `build_flash_read_command_internal(addr, chunkSize)` 生成带CRC的完整帧
2. `port.write_all(&cmd)` 一次性下发命令
3. `port.read_exact(&mut buf)` 阻塞读取完整响应（头+长度+CRC）
4. 调用 `FlashResponse::from_bytes(buf)` 提取纯数据payload
5. 累加到结果 `Vec<u8>` 中
6. `addr += chunkSize`，直到读完 `totalLen`

### 4. 错误处理和重试
- **超时检测**：100ms读取超时保护
- **CRC校验**：自动验证数据完整性（警告但不中断）
- **详细错误信息**：包含具体的地址和错误原因

## API 接口

### Rust 后端函数

```rust
#[tauri::command]
async fn wppg_flash_read_region(
    port_path: String,      // 串口路径，如 "/dev/ttyUSB0"
    baud_rate: u32,         // 波特率，如 115200
    start_addr: u32,        // 起始地址，如 0x10000
    total_len: u32,         // 总读取长度，如 0x800000 - 0x10000
    chunk_size: u16,        // 每次读取的块大小，如 256
) -> Result<Vec<u8>, String>
```

### 前端调用示例

```typescript
const data: number[] = await invoke('wppg_flash_read_region', {
  portPath: '/dev/ttyUSB0',
  baudRate: 115200,
  startAddr: 0x10000,
  totalLen: 0x800000 - 0x10000,  // 8MB - 64KB
  chunkSize: 256
});

// 转换为 Uint8Array
const uint8Data = new Uint8Array(data);

// 保存到文件
await invoke('save_data_to_file', {
  data: Array.from(uint8Data),
  filename: 'flash_data.bin'
});
```

## 参数说明

| 参数 | 类型 | 说明 | 示例值 |
|------|------|------|--------|
| `portPath` | String | 串口设备路径 | `/dev/ttyUSB0` (Linux/Mac)<br>`COM3` (Windows) |
| `baudRate` | u32 | 串口波特率 | `115200` |
| `startAddr` | u32 | Flash起始地址 | `0x10000` (64KB) |
| `totalLen` | u32 | 总读取字节数 | `0x7F0000` (约8MB-64KB) |
| `chunkSize` | u16 | 每次读取块大小 | `256` (1-256字节) |

## 协议兼容性

### 命令格式（基于C#参考实现）
```
[Header] [Command] [Length_L] [Length_H] [Addr_0] [Addr_1] [Addr_2] [Addr_3] [Len_L] [Len_H] [CRC]
  0x77     0x08       0x06      0x00     地址(4字节小端序)    长度(2字节小端序)   CRC8
```

### 响应格式
```
[Header] [Command] [Length_L] [Length_H] [Status] [Data...] [CRC]
  0x77     0x08      数据长度(2字节小端序)   0x00    实际数据    CRC8
```

## 性能特点

- **速度优化**：1ms超时 + 500μs命令间隔
- **内存效率**：预分配缓冲区，避免频繁内存分配
- **进度透明**：自动计算速度和耗时
- **错误恢复**：CRC失败时警告但继续处理

## 使用建议

1. **块大小选择**：推荐256字节，平衡速度和稳定性
2. **地址范围**：根据设备Flash布局调整起始地址和长度
3. **错误处理**：前端应捕获异常并显示详细错误信息
4. **文件保存**：使用 `save_data_to_file` 函数保存到下载目录

## 与传统方式对比

| 特性 | 传统方式 | 一步到位方式 |
|------|----------|-------------|
| 前端调用次数 | 数千次 | 1次 |
| 串口管理 | 前端负责 | 后端统一 |
| 数据解析 | 前端逐个解析 | 后端批量处理 |
| 错误处理 | 分散处理 | 集中处理 |
| 性能 | 受IPC限制 | 原生性能 |
| 代码复杂度 | 高 | 低 |

## 故障排除

### 常见错误

1. **"无法打开串口"**：检查串口路径和权限
2. **"读取超时"**：检查设备连接和波特率
3. **"CRC校验失败"**：数据传输干扰，但会继续处理
4. **"块大小必须在1-256字节之间"**：调整chunkSize参数

### 调试建议

1. 启用日志：查看详细的读取过程
2. 减小块大小：如果出现超时，尝试128或64字节
3. 检查设备状态：确保设备处于正确的通信模式
